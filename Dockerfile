FROM rclone/rclone:beta

COPY ["./docker/startup", "/startup"]
RUN chmod +x /startup

# Optionally, install additional packages if needed
RUN apk --no-cache add \
  fuse3 \
  tzdata \
  && apk cache clean

RUN echo "user_allow_other" >> /etc/fuse.conf

WORKDIR /data

ENV XDG_CONFIG_HOME=/config

# remote configs
ENV REMOTE_NAME=""
ENV REMOTE_URL=""
ENV REMOTE_VENDOR=""

# s3 proxy configs
ENV PROXY_ARGS=""

ENTRYPOINT [ "/startup" ]

EXPOSE 8080
